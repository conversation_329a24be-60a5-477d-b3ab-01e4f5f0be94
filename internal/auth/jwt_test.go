package auth

import (
	"fmt"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/koopa0/pms-api-v2/internal/config"
	"github.com/koopa0/pms-api-v2/internal/testhelpers"
	"github.com/koopa0/pms-api-v2/sqlc"
)

func createTestTokenService() *TokenService {
	return NewTokenService(&config.JWTConfig{
		SecretKey:          "test-secret-key-for-jwt-testing-purposes-only",
		Issuer:             "test-pms-api",
		AccessTokenExpiry:  time.Hour,
		RefreshTokenExpiry: 24 * time.Hour,
	})
}

func TestTokenService_GenerateAccessToken(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name      string
		userID    int32
		email     string
		username  string
		role      sqlc.UserRole
		companyID *int32
		expectErr bool
	}{
		{
			name:      "valid SPO user token",
			userID:    1,
			email:     "<EMAIL>",
			username:  "admin",
			role:      sqlc.UserRoleSPO,
			companyID: nil,
			expectErr: false,
		},
		{
			name:      "valid CISA user token",
			userID:    2,
			email:     "<EMAIL>",
			username:  "cisa_user",
			role:      sqlc.UserRoleCISA,
			companyID: nil,
			expectErr: false,
		},
		{
			name:      "valid company user token",
			userID:    3,
			email:     "<EMAIL>",
			username:  "company_user",
			role:      sqlc.UserRoleCompany,
			companyID: testhelpers.Int32Ptr(10),
			expectErr: false,
		},
		{
			name:      "user with empty email",
			userID:    4,
			email:     "",
			username:  "user",
			role:      sqlc.UserRoleCompany,
			companyID: nil,
			expectErr: false, // JWT generation should still work
		},
		{
			name:      "user with empty username",
			userID:    5,
			email:     "<EMAIL>",
			username:  "",
			role:      sqlc.UserRoleCompany,
			companyID: nil,
			expectErr: false, // JWT generation should still work
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			service := createTestTokenService()

			token, err := service.GenerateAccessToken(tt.userID, tt.email, tt.username, tt.role, tt.companyID)

			if tt.expectErr {
				if err == nil {
					t.Errorf("expected error, but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if token == "" {
				t.Errorf("expected non-empty token")
				return
			}

			// Verify token format (should have 3 parts separated by dots)
			parts := strings.Split(token, ".")
			if len(parts) != 3 {
				t.Errorf("expected JWT to have 3 parts, got %d", len(parts))
			}

			// Verify token can be parsed back
			claims, err := service.ValidateToken(token)
			if err != nil {
				t.Errorf("failed to validate generated token: %v", err)
				return
			}

			// Verify claims
			if claims.UserID != tt.userID {
				t.Errorf("expected UserID %d, got %d", tt.userID, claims.UserID)
			}
			if claims.Email != tt.email {
				t.Errorf("expected Email %s, got %s", tt.email, claims.Email)
			}
			if claims.Username != tt.username {
				t.Errorf("expected Username %s, got %s", tt.username, claims.Username)
			}
			if claims.Role != tt.role {
				t.Errorf("expected Role %v, got %v", tt.role, claims.Role)
			}

			// Verify company ID
			if tt.companyID == nil && claims.CompanyID != nil {
				t.Errorf("expected CompanyID to be nil, got %v", claims.CompanyID)
			}
			if tt.companyID != nil {
				if claims.CompanyID == nil {
					t.Errorf("expected CompanyID %d, got nil", *tt.companyID)
				} else if *claims.CompanyID != *tt.companyID {
					t.Errorf("expected CompanyID %d, got %d", *tt.companyID, *claims.CompanyID)
				}
			}

			// Verify time claims
			if claims.ExpiresAt.Time.Before(time.Now()) {
				t.Errorf("token should not be expired immediately after generation")
			}
			if claims.IssuedAt.Time.After(time.Now()) {
				t.Errorf("token issued time should not be in the future")
			}
			if claims.Issuer != "test-pms-api" {
				t.Errorf("expected issuer 'test-pms-api', got '%s'", claims.Issuer)
			}
		})
	}
}

func TestTokenService_GenerateRefreshToken(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name      string
		userID    int32
		expectErr bool
	}{
		{
			name:      "valid user ID",
			userID:    1,
			expectErr: false,
		},
		{
			name:      "zero user ID",
			userID:    0,
			expectErr: false, // Should still work
		},
		{
			name:      "negative user ID",
			userID:    -1,
			expectErr: false, // Should still work
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			service := createTestTokenService()

			token, err := service.GenerateRefreshToken(tt.userID)

			if tt.expectErr {
				if err == nil {
					t.Errorf("expected error, but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if token == "" {
				t.Errorf("expected non-empty token")
				return
			}

			// Verify token format
			parts := strings.Split(token, ".")
			if len(parts) != 3 {
				t.Errorf("expected JWT to have 3 parts, got %d", len(parts))
			}

			// Verify token can be parsed back
			parsedUserID, err := service.ValidateRefreshToken(token)
			if err != nil {
				t.Errorf("failed to validate generated refresh token: %v", err)
				return
			}

			if parsedUserID != tt.userID {
				t.Errorf("expected UserID %d, got %d", tt.userID, parsedUserID)
			}
		})
	}
}

func TestTokenService_ValidateToken(t *testing.T) {
	t.Parallel()

	service := createTestTokenService()

	tests := []struct {
		name        string
		prepareToken func() string
		expectError bool
		expectClaims *Claims
	}{
		{
			name: "valid token",
			prepareToken: func() string {
				token, _ := service.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
				return token
			},
			expectError: false,
			expectClaims: &Claims{
				UserID:   1,
				Email:    "<EMAIL>",
				Username: "testuser",
				Role:     sqlc.UserRoleSPO,
			},
		},
		{
			name: "empty token",
			prepareToken: func() string {
				return ""
			},
			expectError: true,
		},
		{
			name: "malformed token",
			prepareToken: func() string {
				return "invalid.token.format"
			},
			expectError: true,
		},
		{
			name: "token with wrong signing method",
			prepareToken: func() string {
				// Create a token with RS256 instead of HS256
				claims := &Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     sqlc.UserRoleSPO,
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						Issuer:    "test-pms-api",
					},
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS512, claims)
				tokenString, _ := token.SignedString([]byte("wrong-key"))
				return tokenString
			},
			expectError: true,
		},
		{
			name: "expired token",
			prepareToken: func() string {
				// Create an expired token
				claims := &Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     sqlc.UserRoleSPO,
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired 1 hour ago
						IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
						Issuer:    "test-pms-api",
					},
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, _ := token.SignedString([]byte("test-secret-key-for-jwt-testing-purposes-only"))
				return tokenString
			},
			expectError: true,
		},
		{
			name: "token with wrong secret",
			prepareToken: func() string {
				claims := &Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     sqlc.UserRoleSPO,
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						Issuer:    "test-pms-api",
					},
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, _ := token.SignedString([]byte("wrong-secret-key"))
				return tokenString
			},
			expectError: true,
		},
		{
			name: "valid token with company ID",
			prepareToken: func() string {
				token, _ := service.GenerateAccessToken(3, "<EMAIL>", "companyuser", sqlc.UserRoleCompany, testhelpers.Int32Ptr(10))
				return token
			},
			expectError: false,
			expectClaims: &Claims{
				UserID:    3,
				Email:     "<EMAIL>",
				Username:  "companyuser",
				Role:      sqlc.UserRoleCompany,
				CompanyID: testhelpers.Int32Ptr(10),
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			token := tt.prepareToken()
			claims, err := service.ValidateToken(token)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if tt.expectClaims != nil {
				if claims.UserID != tt.expectClaims.UserID {
					t.Errorf("expected UserID %d, got %d", tt.expectClaims.UserID, claims.UserID)
				}
				if claims.Email != tt.expectClaims.Email {
					t.Errorf("expected Email %s, got %s", tt.expectClaims.Email, claims.Email)
				}
				if claims.Username != tt.expectClaims.Username {
					t.Errorf("expected Username %s, got %s", tt.expectClaims.Username, claims.Username)
				}
				if claims.Role != tt.expectClaims.Role {
					t.Errorf("expected Role %v, got %v", tt.expectClaims.Role, claims.Role)
				}

				// Verify company ID
				if tt.expectClaims.CompanyID == nil && claims.CompanyID != nil {
					t.Errorf("expected CompanyID to be nil, got %v", claims.CompanyID)
				}
				if tt.expectClaims.CompanyID != nil {
					if claims.CompanyID == nil {
						t.Errorf("expected CompanyID %d, got nil", *tt.expectClaims.CompanyID)
					} else if *claims.CompanyID != *tt.expectClaims.CompanyID {
						t.Errorf("expected CompanyID %d, got %d", *tt.expectClaims.CompanyID, *claims.CompanyID)
					}
				}
			}
		})
	}
}

func TestTokenService_ValidateRefreshToken(t *testing.T) {
	t.Parallel()

	service := createTestTokenService()

	tests := []struct {
		name          string
		prepareToken  func() string
		expectError   bool
		expectedUserID int32
	}{
		{
			name: "valid refresh token",
			prepareToken: func() string {
				token, _ := service.GenerateRefreshToken(123)
				return token
			},
			expectError:   false,
			expectedUserID: 123,
		},
		{
			name: "empty token",
			prepareToken: func() string {
				return ""
			},
			expectError: true,
		},
		{
			name: "malformed token",
			prepareToken: func() string {
				return "invalid.token.format"
			},
			expectError: true,
		},
		{
			name: "expired refresh token",
			prepareToken: func() string {
				claims := &jwt.RegisteredClaims{
					Subject:   "456",
					ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired
					IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
					Issuer:    "test-pms-api",
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, _ := token.SignedString([]byte("test-secret-key-for-jwt-testing-purposes-only"))
				return tokenString
			},
			expectError: true,
		},
		{
			name: "refresh token with invalid subject",
			prepareToken: func() string {
				claims := &jwt.RegisteredClaims{
					Subject:   "invalid-user-id", // Non-numeric
					ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
					IssuedAt:  jwt.NewNumericDate(time.Now()),
					Issuer:    "test-pms-api",
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, _ := token.SignedString([]byte("test-secret-key-for-jwt-testing-purposes-only"))
				return tokenString
			},
			expectError: true,
		},
		{
			name: "refresh token with wrong secret",
			prepareToken: func() string {
				claims := &jwt.RegisteredClaims{
					Subject:   "789",
					ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
					IssuedAt:  jwt.NewNumericDate(time.Now()),
					Issuer:    "test-pms-api",
				}
				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, _ := token.SignedString([]byte("wrong-secret-key"))
				return tokenString
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			token := tt.prepareToken()
			userID, err := service.ValidateRefreshToken(token)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error, but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if userID != tt.expectedUserID {
				t.Errorf("expected UserID %d, got %d", tt.expectedUserID, userID)
			}
		})
	}
}

func TestTokenService_TokenLifecycle(t *testing.T) {
	t.Parallel()

	service := createTestTokenService()

	// Generate access token
	accessToken, err := service.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
	if err != nil {
		t.Fatalf("failed to generate access token: %v", err)
	}

	// Generate refresh token
	refreshToken, err := service.GenerateRefreshToken(1)
	if err != nil {
		t.Fatalf("failed to generate refresh token: %v", err)
	}

	// Validate access token
	claims, err := service.ValidateToken(accessToken)
	if err != nil {
		t.Fatalf("failed to validate access token: %v", err)
	}

	if claims.UserID != 1 {
		t.Errorf("expected UserID 1, got %d", claims.UserID)
	}

	// Validate refresh token
	userID, err := service.ValidateRefreshToken(refreshToken)
	if err != nil {
		t.Fatalf("failed to validate refresh token: %v", err)
	}

	if userID != 1 {
		t.Errorf("expected UserID 1, got %d", userID)
	}

	// Generate new access token using refresh token
	newAccessToken, err := service.GenerateAccessToken(userID, claims.Email, claims.Username, claims.Role, claims.CompanyID)
	if err != nil {
		t.Fatalf("failed to generate new access token: %v", err)
	}

	// Validate new access token
	newClaims, err := service.ValidateToken(newAccessToken)
	if err != nil {
		t.Fatalf("failed to validate new access token: %v", err)
	}

	// Verify that claims are preserved
	if newClaims.UserID != claims.UserID {
		t.Errorf("UserID mismatch: expected %d, got %d", claims.UserID, newClaims.UserID)
	}
	if newClaims.Email != claims.Email {
		t.Errorf("Email mismatch: expected %s, got %s", claims.Email, newClaims.Email)
	}
}

// Benchmark tests
func BenchmarkTokenService_GenerateAccessToken(b *testing.B) {
	service := createTestTokenService()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

func BenchmarkTokenService_ValidateToken(b *testing.B) {
	service := createTestTokenService()

	// Generate a token to validate
	token, err := service.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
	if err != nil {
		b.Fatalf("failed to generate token: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.ValidateToken(token)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

func BenchmarkTokenService_GenerateRefreshToken(b *testing.B) {
	service := createTestTokenService()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.GenerateRefreshToken(1)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

func BenchmarkTokenService_ValidateRefreshToken(b *testing.B) {
	service := createTestTokenService()

	// Generate a refresh token to validate
	token, err := service.GenerateRefreshToken(1)
	if err != nil {
		b.Fatalf("failed to generate refresh token: %v", err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.ValidateRefreshToken(token)
		if err != nil {
			b.Errorf("unexpected error: %v", err)
		}
	}
}

// Fuzz test for token validation (commented out as it requires Go 1.18+ fuzzing support)
/*
func FuzzTokenService_ValidateToken(f *testing.F) {
	service := createTestTokenService()

	// Add some seed inputs
	f.Add("eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9")
	f.Add("invalid.token.string")
	f.Add("")
	f.Add("...")

	f.Fuzz(func(t *testing.T, token string) {
		// Fuzzing should not cause panics
		_, err := service.ValidateToken(token)
		// We expect most fuzz inputs to be invalid, so we don't check the error
		_ = err
	})
}
*/