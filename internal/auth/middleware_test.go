package auth

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/config"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/sqlc"
)

func createTestMiddleware() (*Middleware, *TokenService) {
	tokenService := createTestTokenService()
	middleware := NewMiddleware(tokenService)
	return middleware, tokenService
}

// Test handler that checks if user context was set
func testHandler(w http.ResponseWriter, r *http.Request) {
	claims, ok := GetUserFromContext(r.Context())
	if ok {
		w.Header().Set("X-User-ID", string(rune(claims.UserID)))
		w.<PERSON><PERSON>().Set("X-User-Email", claims.Email)
		w.Header().Set("X-User-Role", string(claims.Role))
	}
	w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
	w.Write([]byte("success"))
}

func TestMiddleware_Authenticate(t *testing.T) {
	t.<PERSON>llel()

	middleware, tokenService := createTestMiddleware()

	// Generate valid token
	validToken, err := tokenService.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
	if err != nil {
		t.Fatalf("failed to generate test token: %v", err)
	}

	tests := []struct {
		name           string
		setupRequest   func(*http.Request)
		expectedStatus int
		expectedBody   string
		checkContext   bool
	}{
		{
			name: "valid token in cookie",
			setupRequest: func(req *http.Request) {
				req.AddCookie(&http.Cookie{
					Name:  "auth",
					Value: validToken,
				})
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			checkContext:   true,
		},
		{
			name: "valid token in Authorization header",
			setupRequest: func(req *http.Request) {
				req.Header.Set("Authorization", "Bearer "+validToken)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			checkContext:   true,
		},
		{
			name: "excluded path /health",
			setupRequest: func(req *http.Request) {
				req.URL.Path = "/health"
				// No auth token provided
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			checkContext:   false,
		},
		{
			name: "excluded path /api/v1/login",
			setupRequest: func(req *http.Request) {
				req.URL.Path = "/api/v1/login"
				// No auth token provided
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			checkContext:   false,
		},
		{
			name: "excluded path /api/v1/register",
			setupRequest: func(req *http.Request) {
				req.URL.Path = "/api/v1/register"
				// No auth token provided
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
			checkContext:   false,
		},
		{
			name: "no auth token",
			setupRequest: func(req *http.Request) {
				req.URL.Path = "/api/v1/protected"
				// No auth token provided
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"unauthorized"}`,
			checkContext:   false,
		},
		{
			name: "invalid token in cookie",
			setupRequest: func(req *http.Request) {
				req.AddCookie(&http.Cookie{
					Name:  "auth",
					Value: "invalid-token",
				})
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"invalid token"}`,
			checkContext:   false,
		},
		{
			name: "invalid Authorization header format",
			setupRequest: func(req *http.Request) {
				req.Header.Set("Authorization", "InvalidFormat")
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"invalid authorization header"}`,
			checkContext:   false,
		},
		{
			name: "missing Bearer prefix",
			setupRequest: func(req *http.Request) {
				req.Header.Set("Authorization", "Token "+validToken)
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"invalid authorization header"}`,
			checkContext:   false,
		},
		{
			name: "invalid token in Authorization header",
			setupRequest: func(req *http.Request) {
				req.Header.Set("Authorization", "Bearer invalid-token")
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"invalid token"}`,
			checkContext:   false,
		},
		{
			name: "expired token",
			setupRequest: func(req *http.Request) {
				// Create expired token service
				expiredTokenService := NewTokenService(&config.JWTConfig{
					SecretKey:          "test-secret-key-for-jwt-testing-purposes-only",
					Issuer:             "test-pms-api",
					AccessTokenExpiry:  -time.Hour, // Expired 1 hour ago
					RefreshTokenExpiry: 24 * time.Hour,
				})
				expiredToken, _ := expiredTokenService.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
				req.AddCookie(&http.Cookie{
					Name:  "auth",
					Value: expiredToken,
				})
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"invalid token"}`,
			checkContext:   false,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/test", nil)
			tt.setupRequest(req)

			// Create response recorder
			w := httptest.NewRecorder()

			// Execute middleware
			handler := middleware.Authenticate(testHandler)
			handler(w, req)

			// Check status
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
				return
			}

			// Check body
			body := strings.TrimSpace(w.Body.String())
			if body != tt.expectedBody {
				t.Errorf("expected body '%s', got '%s'", tt.expectedBody, body)
			}

			// Check context if expected
			if tt.checkContext && w.Code == http.StatusOK {
				userID := w.Header().Get("X-User-ID")
				userEmail := w.Header().Get("X-User-Email")
				userRole := w.Header().Get("X-User-Role")

				if userID == "" || userEmail == "" || userRole == "" {
					t.Errorf("expected user context to be set, but headers are missing")
				}
			}
		})
	}
}

func TestMiddleware_RequireRole(t *testing.T) {
	t.Parallel()

	middleware, tokenService := createTestMiddleware()

	// Generate tokens for different roles
	spoToken, _ := tokenService.GenerateAccessToken(1, "<EMAIL>", "spo", sqlc.UserRoleSPO, nil)
	cisaToken, _ := tokenService.GenerateAccessToken(2, "<EMAIL>", "cisa", sqlc.UserRoleCISA, nil)
	companyToken, _ := tokenService.GenerateAccessToken(3, "<EMAIL>", "company", sqlc.UserRoleCompany, testutils.Int32Ptr(10))

	tests := []struct {
		name           string
		requiredRoles  []sqlc.UserRole
		userToken      string
		setupContext   func(*http.Request) *http.Request
		expectedStatus int
		expectedBody   string
	}{
		{
			name:          "SPO user accessing SPO-only endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO},
			userToken:     spoToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(spoToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
		},
		{
			name:          "CISA user accessing CISA-only endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleCISA},
			userToken:     cisaToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(cisaToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
		},
		{
			name:          "Company user accessing Company-only endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleCompany},
			userToken:     companyToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(companyToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
		},
		{
			name:          "SPO user accessing multi-role endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO, sqlc.UserRoleCISA},
			userToken:     spoToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(spoToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
		},
		{
			name:          "CISA user accessing multi-role endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO, sqlc.UserRoleCISA},
			userToken:     cisaToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(cisaToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "success",
		},
		{
			name:          "Company user accessing SPO-only endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO},
			userToken:     companyToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(companyToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   `{"error":"forbidden"}`,
		},
		{
			name:          "Company user accessing CISA-only endpoint",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleCISA},
			userToken:     companyToken,
			setupContext: func(req *http.Request) *http.Request {
				claims, _ := tokenService.ValidateToken(companyToken)
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusForbidden,
			expectedBody:   `{"error":"forbidden"}`,
		},
		{
			name:          "No user context",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO},
			userToken:     "",
			setupContext: func(req *http.Request) *http.Request {
				// Return request without user context
				return req
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"unauthorized"}`,
		},
		{
			name:          "Invalid user context type",
			requiredRoles: []sqlc.UserRole{sqlc.UserRoleSPO},
			userToken:     "",
			setupContext: func(req *http.Request) *http.Request {
				// Add invalid context value
				ctx := context.WithValue(req.Context(), ContextKeyUser, "invalid-claims")
				return req.WithContext(ctx)
			},
			expectedStatus: http.StatusUnauthorized,
			expectedBody:   `{"error":"unauthorized"}`,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/admin", nil)
			req = tt.setupContext(req)

			// Create response recorder
			w := httptest.NewRecorder()

			// Execute role middleware
			handler := middleware.RequireRole(tt.requiredRoles...)(testHandler)
			handler(w, req)

			// Check status
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
				return
			}

			// Check body
			body := strings.TrimSpace(w.Body.String())
			if body != tt.expectedBody {
				t.Errorf("expected body '%s', got '%s'", tt.expectedBody, body)
			}
		})
	}
}

func TestGetUserFromContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedOK     bool
		expectedClaims *Claims
	}{
		{
			name: "valid user context",
			setupContext: func() context.Context {
				claims := &Claims{
					UserID:   1,
					Email:    "<EMAIL>",
					Username: "testuser",
					Role:     sqlc.UserRoleSPO,
				}
				return context.WithValue(context.Background(), ContextKeyUser, claims)
			},
			expectedOK: true,
			expectedClaims: &Claims{
				UserID:   1,
				Email:    "<EMAIL>",
				Username: "testuser",
				Role:     sqlc.UserRoleSPO,
			},
		},
		{
			name: "no user context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedOK:     false,
			expectedClaims: nil,
		},
		{
			name: "invalid context type",
			setupContext: func() context.Context {
				return context.WithValue(context.Background(), ContextKeyUser, "invalid")
			},
			expectedOK:     false,
			expectedClaims: nil,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := tt.setupContext()
			claims, ok := GetUserFromContext(ctx)

			if ok != tt.expectedOK {
				t.Errorf("expected ok %v, got %v", tt.expectedOK, ok)
				return
			}

			if tt.expectedOK && tt.expectedClaims != nil {
				if claims.UserID != tt.expectedClaims.UserID {
					t.Errorf("expected UserID %d, got %d", tt.expectedClaims.UserID, claims.UserID)
				}
				if claims.Email != tt.expectedClaims.Email {
					t.Errorf("expected Email %s, got %s", tt.expectedClaims.Email, claims.Email)
				}
				if claims.Username != tt.expectedClaims.Username {
					t.Errorf("expected Username %s, got %s", tt.expectedClaims.Username, claims.Username)
				}
				if claims.Role != tt.expectedClaims.Role {
					t.Errorf("expected Role %v, got %v", tt.expectedClaims.Role, claims.Role)
				}
			}
		})
	}
}

func TestGetUserIDFromContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupContext   func() context.Context
		expectedOK     bool
		expectedUserID int32
	}{
		{
			name: "valid user context",
			setupContext: func() context.Context {
				claims := &Claims{UserID: 123}
				return context.WithValue(context.Background(), ContextKeyUser, claims)
			},
			expectedOK:     true,
			expectedUserID: 123,
		},
		{
			name: "no user context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedOK:     false,
			expectedUserID: 0,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := tt.setupContext()
			userID, ok := GetUserIDFromContext(ctx)

			if ok != tt.expectedOK {
				t.Errorf("expected ok %v, got %v", tt.expectedOK, ok)
			}

			if userID != tt.expectedUserID {
				t.Errorf("expected UserID %d, got %d", tt.expectedUserID, userID)
			}
		})
	}
}

func TestGetUserRoleFromContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		setupContext func() context.Context
		expectedOK   bool
		expectedRole sqlc.UserRole
	}{
		{
			name: "valid user context",
			setupContext: func() context.Context {
				claims := &Claims{Role: sqlc.UserRoleSPO}
				return context.WithValue(context.Background(), ContextKeyUser, claims)
			},
			expectedOK:   true,
			expectedRole: sqlc.UserRoleSPO,
		},
		{
			name: "no user context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedOK:   false,
			expectedRole: "",
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := tt.setupContext()
			role, ok := GetUserRoleFromContext(ctx)

			if ok != tt.expectedOK {
				t.Errorf("expected ok %v, got %v", tt.expectedOK, ok)
			}

			if role != tt.expectedRole {
				t.Errorf("expected Role %v, got %v", tt.expectedRole, role)
			}
		})
	}
}

func TestSetAuthCookie(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		token          string
		isProduction   bool
		validateCookie func(*http.Cookie) bool
	}{
		{
			name:         "development cookie",
			token:        "test-token",
			isProduction: false,
			validateCookie: func(cookie *http.Cookie) bool {
				return cookie.Name == "auth" &&
					cookie.Value == "test-token" &&
					cookie.Path == "/" &&
					cookie.HttpOnly &&
					!cookie.Secure && // Should be false in development
					cookie.SameSite == http.SameSiteStrictMode &&
					cookie.MaxAge == 86400
			},
		},
		{
			name:         "production cookie",
			token:        "prod-token",
			isProduction: true,
			validateCookie: func(cookie *http.Cookie) bool {
				return cookie.Name == "auth" &&
					cookie.Value == "prod-token" &&
					cookie.Path == "/" &&
					cookie.HttpOnly &&
					cookie.Secure && // Should be true in production
					cookie.SameSite == http.SameSiteStrictMode &&
					cookie.MaxAge == 86400
			},
		},
		{
			name:         "empty token",
			token:        "",
			isProduction: false,
			validateCookie: func(cookie *http.Cookie) bool {
				return cookie.Name == "auth" &&
					cookie.Value == "" &&
					cookie.HttpOnly
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			w := httptest.NewRecorder()
			SetAuthCookie(w, tt.token, tt.isProduction)

			// Check Set-Cookie header
			setCookieHeader := w.Header().Get("Set-Cookie")
			if setCookieHeader == "" {
				t.Errorf("expected Set-Cookie header, but it was empty")
				return
			}

			// For testing, we'll manually parse the Set-Cookie header
			// since http.Request.Cookie() expects a different format

			// Basic validation that the header contains expected values
			if !strings.Contains(setCookieHeader, "auth=") {
				t.Errorf("expected cookie name 'auth' in Set-Cookie header")
			}

			if tt.token != "" && !strings.Contains(setCookieHeader, tt.token) {
				t.Errorf("expected token '%s' in Set-Cookie header", tt.token)
			}

			if !strings.Contains(setCookieHeader, "HttpOnly") {
				t.Errorf("expected HttpOnly flag in Set-Cookie header")
			}

			if tt.isProduction && !strings.Contains(setCookieHeader, "Secure") {
				t.Errorf("expected Secure flag in production cookie")
			}

			if !tt.isProduction && strings.Contains(setCookieHeader, "Secure") {
				t.Errorf("unexpected Secure flag in development cookie")
			}
		})
	}
}

func TestClearAuthCookie(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		isProduction bool
	}{
		{
			name:         "clear development cookie",
			isProduction: false,
		},
		{
			name:         "clear production cookie",
			isProduction: true,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			w := httptest.NewRecorder()
			ClearAuthCookie(w, tt.isProduction)

			// Check Set-Cookie header
			setCookieHeader := w.Header().Get("Set-Cookie")
			if setCookieHeader == "" {
				t.Errorf("expected Set-Cookie header, but it was empty")
				return
			}

			// Validate cookie clearing attributes
			if !strings.Contains(setCookieHeader, "auth=") {
				t.Errorf("expected cookie name 'auth' in Set-Cookie header")
			}

			if !strings.Contains(setCookieHeader, "MaxAge=-1") && !strings.Contains(setCookieHeader, "Max-Age=-1") {
				t.Errorf("expected MaxAge=-1 to delete cookie")
			}

			if !strings.Contains(setCookieHeader, "HttpOnly") {
				t.Errorf("expected HttpOnly flag in Set-Cookie header")
			}

			if tt.isProduction && !strings.Contains(setCookieHeader, "Secure") {
				t.Errorf("expected Secure flag in production cookie")
			}
		})
	}
}

// Integration test combining authentication and role check
func TestMiddleware_FullAuthFlow(t *testing.T) {
	t.Parallel()

	middleware, tokenService := createTestMiddleware()

	// Generate tokens for different roles
	spoToken, _ := tokenService.GenerateAccessToken(1, "<EMAIL>", "spo", sqlc.UserRoleSPO, nil)

	// Create a handler that requires SPO role
	protectedHandler := middleware.RequireRole(sqlc.UserRoleSPO)(testHandler)
	fullHandler := middleware.Authenticate(protectedHandler)

	tests := []struct {
		name           string
		setupRequest   func(*http.Request)
		expectedStatus int
	}{
		{
			name: "valid SPO user accessing SPO endpoint",
			setupRequest: func(req *http.Request) {
				req.AddCookie(&http.Cookie{
					Name:  "auth",
					Value: spoToken,
				})
			},
			expectedStatus: http.StatusOK,
		},
		{
			name: "unauthenticated user accessing SPO endpoint",
			setupRequest: func(req *http.Request) {
				// No auth token
			},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name: "invalid token accessing SPO endpoint",
			setupRequest: func(req *http.Request) {
				req.AddCookie(&http.Cookie{
					Name:  "auth",
					Value: "invalid-token",
				})
			},
			expectedStatus: http.StatusUnauthorized,
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(http.MethodGet, "/api/v1/admin", nil)
			tt.setupRequest(req)

			w := httptest.NewRecorder()
			fullHandler(w, req)

			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
			}
		})
	}
}

// Benchmark tests
func BenchmarkMiddleware_Authenticate(b *testing.B) {
	middleware, tokenService := createTestMiddleware()

	// Generate valid token
	validToken, _ := tokenService.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)

	handler := middleware.Authenticate(testHandler)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/api/v1/test", nil)
		req.AddCookie(&http.Cookie{
			Name:  "auth",
			Value: validToken,
		})

		w := httptest.NewRecorder()
		handler(w, req)
	}
}

func BenchmarkMiddleware_RequireRole(b *testing.B) {
	middleware, tokenService := createTestMiddleware()

	// Generate valid token
	validToken, _ := tokenService.GenerateAccessToken(1, "<EMAIL>", "testuser", sqlc.UserRoleSPO, nil)
	claims, _ := tokenService.ValidateToken(validToken)

	handler := middleware.RequireRole(sqlc.UserRoleSPO)(testHandler)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodGet, "/api/v1/admin", nil)
		ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()
		handler(w, req)
	}
}
