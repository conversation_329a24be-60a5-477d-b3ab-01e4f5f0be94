// Package database 提供資料庫連線管理功能
// 使用 pgx v5 原生驅動程式與 PostgreSQL 通訊
// 支援連線池、健康檢查、交易管理和類型安全查詢
package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/koopa0/pms-api-v2/internal/config"
)

// DB 封裝 pgx 連線池
// 提供類型安全的資料庫操作和連線池管理
type DB struct {
	pool *pgxpool.Pool
}

// New 建立新的資料庫連線池
// 使用 pgx v5 原生驅動程式和連線池管理
// 返回已測試可用的資料庫連線池
func New(cfg *config.DatabaseConfig) (*DB, error) {
	// 解析連線配置
	poolConfig, err := pgxpool.ParseConfig(cfg.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database config: %w", err)
	}

	// 設定連線池參數（遵循 Architecture.md 中的 PostgreSQL 17+ 最佳實踐）
	poolConfig.MaxConns = int32(cfg.MaxConnections) // 基於 CPU 核心數
	poolConfig.MinConns = int32(cfg.MaxIdleConns)   // 保持最小連線數
	poolConfig.MaxConnLifetime = cfg.MaxLifetime    // 連線輪換
	poolConfig.MaxConnIdleTime = 15 * time.Minute   // 關閉閒置連線
	poolConfig.HealthCheckPeriod = time.Minute      // 定期健康檢查

	// 建立連線池
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// 測試資料庫連線
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &DB{pool: pool}, nil
}

// Close 關閉資料庫連線池
// 應該在應用程式結束時呼叫
func (db *DB) Close() {
	db.pool.Close()
}

// Health 檢查資料庫健康狀態
// 用於健康檢查端點，確保資料庫連線正常
// 會執行 ping 和簡單查詢來驗證連線
func (db *DB) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 1*time.Second)
	defer cancel()

	if err := db.pool.Ping(ctx); err != nil {
		return fmt.Errorf("database health check failed: %w", err)
	}

	// 檢查是否可以執行簡單查詢
	var result int
	if err := db.pool.QueryRow(ctx, "SELECT 1").Scan(&result); err != nil {
		return fmt.Errorf("database query check failed: %w", err)
	}

	return nil
}

// Pool 返回底層的 pgx 連線池
// 用於需要直接存取連線池的操作
func (db *DB) Pool() *pgxpool.Pool {
	return db.pool
}

// BeginTx 開始一個新的交易
// 返回 pgx.Tx 用於類型安全的交易操作
func (db *DB) BeginTx(ctx context.Context) (pgx.Tx, error) {
	return db.pool.Begin(ctx)
}

// Query 執行查詢並返回 pgx.Rows
// 提供類型安全的查詢操作
func (db *DB) Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	return db.pool.Query(ctx, sql, args...)
}

// QueryRow 執行查詢並返回單一行
// 提供類型安全的單行查詢操作
func (db *DB) QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	return db.pool.QueryRow(ctx, sql, args...)
}

// Exec 執行 SQL 命令
// 返回受影響的行數
func (db *DB) Exec(ctx context.Context, sql string, args ...interface{}) (pgxpool.CommandTag, error) {
	return db.pool.Exec(ctx, sql, args...)
}

// Stats 返回連線池統計資訊
// 用於監控和調試連線池狀態
func (db *DB) Stats() *pgxpool.Stat {
	return db.pool.Stat()
}
