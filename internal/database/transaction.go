package database

import (
	"context"
	"fmt"

	"github.com/jackc/pgx/v5"
)

// TxFunc 代表在資料庫交易中執行的函數
// 接受 context 和交易物件，返回錯誤以決定是否回滾
type TxFunc func(context.Context, *sql.Tx) error

// WithTx 在資料庫交易中執行給定的函數
// 自動處理交易的開始、提交和回滾
// 如果函數返回錯誤，交易會自動回滾
func (db *DB) WithTx(ctx context.Context, fn TxFunc) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("begin transaction: %w", err)
	}

	if err := fn(ctx, tx); err != nil {
		if rbErr := tx.Rollback(); rbErr != nil {
			return fmt.Errorf("rollback transaction: %v (original error: %w)", rbErr, err)
		}
		return err
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("commit transaction: %w", err)
	}

	return nil
}

// WithTxOptions 在帶有自訂選項的資料庫交易中執行給定的函數
// 可以設定交易的隔離級別和只讀模式
// 適用於需要特定交易行為的場景
func (db *DB) WithTxOptions(ctx context.Context, opts *sql.TxOptions, fn TxFunc) error {
	tx, err := db.BeginTx(ctx, opts)
	if err != nil {
		return fmt.Errorf("begin transaction: %w", err)
	}

	if err := fn(ctx, tx); err != nil {
		if rbErr := tx.Rollback(); rbErr != nil {
			return fmt.Errorf("rollback transaction: %v (original error: %w)", rbErr, err)
		}
		return err
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("commit transaction: %w", err)
	}

	return nil
}
