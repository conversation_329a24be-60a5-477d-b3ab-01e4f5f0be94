package testutils

import (
	"context"
	"database/sql"
	"sync"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/sqlc"
)

// DatabaseTestHelper provides helpers for database testing
// This follows Architecture.md guidelines for testing database operations
type DatabaseTestHelper struct {
	db       *sql.DB
	queries  *sqlc.Queries
	mu       sync.RWMutex
	testData map[string]interface{}
}

// NewDatabaseTestHelper creates a new database test helper
func NewDatabaseTestHelper(db *sql.DB) *DatabaseTestHelper {
	return &DatabaseTestHelper{
		db:       db,
		queries:  sqlc.New(db),
		testData: make(map[string]interface{}),
	}
}

// SeedTestData inserts common test data for testing
func (h *DatabaseTestHelper) SeedTestData(t *testing.T) {
	t.Helper()

	ctx := context.Background()

	// Create test users
	adminUser := h.CreateTestUser(t, TestUser{
		Username: "admin",
		Email:    "<EMAIL>",
		Role:     "SPO",
		Password: "AdminPass123!",
	})

	cisaUser := h.CreateTestUser(t, TestUser{
		Username: "cisa_user",
		Email:    "<EMAIL>",
		Role:     "CISA",
		Password: "CisaPass123!",
	})

	companyUser := h.CreateTestUser(t, TestUser{
		Username: "company_user",
		Email:    "<EMAIL>",
		Role:     "Company",
		Password: "CompanyPass123!",
	})

	// Store test data for later use
	h.mu.Lock()
	h.testData["admin_user"] = adminUser
	h.testData["cisa_user"] = cisaUser
	h.testData["company_user"] = companyUser
	h.mu.Unlock()

	t.Logf("Seeded test data: admin=%d, cisa=%d, company=%d",
		adminUser.ID, cisaUser.ID, companyUser.ID)
}

// CleanupTestData removes all test data
func (h *DatabaseTestHelper) CleanupTestData(t *testing.T) {
	t.Helper()

	ctx := context.Background()

	// Clean up in reverse order of dependencies
	_, err := h.db.ExecContext(ctx, "DELETE FROM projects WHERE created_by IN (SELECT id FROM users WHERE email LIKE '%test%' OR email LIKE '%example%')")
	if err != nil {
		t.Logf("Warning: failed to cleanup test projects: %v", err)
	}

	_, err = h.db.ExecContext(ctx, "DELETE FROM users WHERE email LIKE '%test%' OR email LIKE '%example%'")
	if err != nil {
		t.Logf("Warning: failed to cleanup test users: %v", err)
	}

	// Clear test data
	h.mu.Lock()
	h.testData = make(map[string]interface{})
	h.mu.Unlock()
}

// TestUser represents a test user for database operations
type TestUser struct {
	ID       int32
	Username string
	Email    string
	Role     string
	Password string
}

// CreateTestUser creates a test user in the database
func (h *DatabaseTestHelper) CreateTestUser(t *testing.T, user TestUser) TestUser {
	t.Helper()

	// In a real implementation, this would use the actual user creation logic
	// For now, we'll simulate the creation
	createdUser := sqlc.User{
		ID:        user.ID,
		Username:  user.Username,
		Email:     user.Email,
		UserRole:  sqlc.UserRole(user.Role),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return TestUser{
		ID:       createdUser.ID,
		Username: createdUser.Username,
		Email:    createdUser.Email,
		Role:     string(createdUser.UserRole),
		Password: user.Password,
	}
}

// GetTestUser retrieves a test user by key
func (h *DatabaseTestHelper) GetTestUser(key string) (TestUser, bool) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if user, exists := h.testData[key]; exists {
		if testUser, ok := user.(TestUser); ok {
			return testUser, true
		}
	}

	return TestUser{}, false
}

// InMemoryUserStore provides an in-memory implementation for testing
type InMemoryUserStore struct {
	users  map[int32]sqlc.User
	mu     sync.RWMutex
	nextID int32
}

// NewInMemoryUserStore creates a new in-memory user store
func NewInMemoryUserStore() *InMemoryUserStore {
	return &InMemoryUserStore{
		users:  make(map[int32]sqlc.User),
		nextID: 1,
	}
}

// CreateUser creates a user in memory
func (s *InMemoryUserStore) CreateUser(user sqlc.User) (sqlc.User, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if user.ID == 0 {
		user.ID = s.nextID
		s.nextID++
	}

	user.CreatedAt = time.Now()
	user.UpdatedAt = time.Now()

	s.users[user.ID] = user
	return user, nil
}

// GetUserByID retrieves a user by ID
func (s *InMemoryUserStore) GetUserByID(id int32) (sqlc.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if user, exists := s.users[id]; exists {
		return user, nil
	}

	return sqlc.User{}, sql.ErrNoRows
}

// GetUserByEmail retrieves a user by email
func (s *InMemoryUserStore) GetUserByEmail(email string) (sqlc.User, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	for _, user := range s.users {
		if user.Email == email {
			return user, nil
		}
	}

	return sqlc.User{}, sql.ErrNoRows
}

// UpdateUser updates a user in memory
func (s *InMemoryUserStore) UpdateUser(user sqlc.User) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.users[user.ID]; !exists {
		return sql.ErrNoRows
	}

	user.UpdatedAt = time.Now()
	s.users[user.ID] = user
	return nil
}

// DeleteUser deletes a user from memory
func (s *InMemoryUserStore) DeleteUser(id int32) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if _, exists := s.users[id]; !exists {
		return sql.ErrNoRows
	}

	delete(s.users, id)
	return nil
}

// ListUsers returns all users
func (s *InMemoryUserStore) ListUsers() []sqlc.User {
	s.mu.RLock()
	defer s.mu.RUnlock()

	users := make([]sqlc.User, 0, len(s.users))
	for _, user := range s.users {
		users = append(users, user)
	}

	return users
}

// Reset clears all users
func (s *InMemoryUserStore) Reset() {
	s.mu.Lock()
	defer s.mu.Unlock()

	s.users = make(map[int32]sqlc.User)
	s.nextID = 1
}

// TransactionTestHelper provides utilities for testing database transactions
type TransactionTestHelper struct {
	db *sql.DB
}

// NewTransactionTestHelper creates a new transaction test helper
func NewTransactionTestHelper(db *sql.DB) *TransactionTestHelper {
	return &TransactionTestHelper{db: db}
}

// WithTransaction executes a function within a database transaction for testing
func (h *TransactionTestHelper) WithTransaction(t *testing.T, fn func(*sql.Tx) error) error {
	t.Helper()

	tx, err := h.db.Begin()
	if err != nil {
		return err
	}

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()

	err = fn(tx)
	return err
}
