package user

import (
	"context"
	"database/sql"
	"errors"

	"github.com/koopa0/pms-api-v2/internal/auth"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service provides user management operations
type Service struct {
	queries         *sqlc.Queries
	passwordService *auth.PasswordService
	logger          *logger.Logger
}

// NewService creates a new user service
func NewService(queries *sqlc.Queries, logger *logger.Logger) *Service {
	return &Service{
		queries:         queries,
		passwordService: auth.NewPasswordService(),
		logger:          logger,
	}
}

// UpdateUserRequest represents user update parameters
type UpdateUserRequest struct {
	Username    string  `json:"username"`
	Email       string  `json:"email"`
	BackupEmail *string `json:"backup_email"`
	JobTitle    *string `json:"job_title"`
	Mobile      *string `json:"mobile"`
}

// ChangePasswordRequest represents password change parameters
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password"`
}

// Info UserInfo represents user information
type Info struct {
	ID          int32           `json:"id"`
	Username    string          `json:"username"`
	Email       string          `json:"email"`
	Role        sqlc.UserRole   `json:"role"`
	Status      sqlc.UserStatus `json:"status"`
	CompanyID   *int32          `json:"company_id,omitempty"`
	CompanyName *string         `json:"company_name,omitempty"`
	JobTitle    *string         `json:"job_title,omitempty"`
	Mobile      *string         `json:"mobile,omitempty"`
}

// ListUsersRequest represents user listing parameters
type ListUsersRequest struct {
	Role       sqlc.UserRole            `json:"role"`
	Status     sqlc.UserStatus          `json:"status"`
	Pagination types.PaginationParams   `json:"pagination"`
}

// ListUsersResponse represents user listing response
type ListUsersResponse = types.PaginatedResponse[Info]

// UpdateUser updates user information
func (s *Service) UpdateUser(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error) {
	// Convert pointers to sql.Null types using helpers
	backupEmail := types.NullStringFromPtr(req.BackupEmail)
	jobTitle := types.NullStringFromPtr(req.JobTitle)
	mobile := types.NullStringFromPtr(req.Mobile)

	user, err := s.queries.UpdateUser(ctx, sqlc.UpdateUserParams{
		ID:          userID,
		Username:    req.Username,
		Email:       req.Email,
		BackupEmail: backupEmail,
		JobTitle:    jobTitle,
		Mobile:      mobile,
	})
	if err != nil {
		s.logger.Error("failed to update user", "error", err, "user_id", userID)
		return nil, ErrInternalError
	}

	return s.mapUpdateUserRowToUserInfo(&user), nil
}

// ChangePassword changes user password
func (s *Service) ChangePassword(ctx context.Context, userID int32, req ChangePasswordRequest) error {
	// Get current user
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return ErrUserNotFound
		}
		s.logger.Error("failed to get user for password change", "error", err, "user_id", userID)
		return ErrInternalError
	}

	// Verify old password
	if err := s.passwordService.VerifyPassword(req.OldPassword, user.PasswordHash); err != nil {
		return ErrInvalidPassword
	}

	// Validate new password strength
	if err = s.passwordService.ValidatePasswordStrength(req.NewPassword); err != nil {
		return ErrWeakPassword
	}

	// Hash new password
	hashedPassword, err := s.passwordService.HashPassword(req.NewPassword)
	if err != nil {
		s.logger.Error("failed to hash password", "error", err, "user_id", userID)
		return ErrInternalError
	}

	// Update password
	if err = s.queries.UpdateUserPassword(ctx, sqlc.UpdateUserPasswordParams{
		ID:           userID,
		PasswordHash: hashedPassword,
	}); err != nil {
		s.logger.Error("failed to update password", "error", err, "user_id", userID)
		return ErrInternalError
	}

	return nil
}

// GetUserByID retrieves user by ID
func (s *Service) GetUserByID(ctx context.Context, userID int32) (*Info, error) {
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		s.logger.Error("failed to get user by ID", "error", err, "user_id", userID)
		return nil, ErrInternalError
	}

	return s.mapGetUserByIDRowToUserInfo(&user), nil
}

// ListUsers retrieves users with pagination and filters
func (s *Service) ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 || req.PageSize > 100 {
		req.PageSize = 20
	}

	offset := (req.Page - 1) * req.PageSize

	// Convert filters to SQL parameters
	var roleFilter sqlc.UserRole
	var statusFilter sqlc.UserStatus
	if req.Role != "" {
		roleFilter = req.Role
	}
	if req.Status != "" {
		statusFilter = req.Status
	}

	// Safely convert to int32 with bounds checking
	limit := req.PageSize
	if limit > 2147483647 {
		limit = 2147483647
	}
	offsetInt32 := offset
	if offsetInt32 > 2147483647 {
		offsetInt32 = 2147483647
	}

	// Get users
	users, err := s.queries.ListUsers(ctx, sqlc.ListUsersParams{
		Column1: string(roleFilter),
		Column2: string(statusFilter),
		Limit:   int32(limit),
		Offset:  int32(offsetInt32),
	})
	if err != nil {
		s.logger.Error("failed to list users", "error", err)
		return nil, ErrInternalError
	}

	// Get total count
	count, err := s.queries.CountUsers(ctx, sqlc.CountUsersParams{
		Column1: string(roleFilter),
		Column2: string(statusFilter),
	})
	if err != nil {
		s.logger.Error("failed to count users", "error", err)
		return nil, ErrInternalError
	}

	// Convert to response
	userInfos := make([]Info, len(users))
	for i, user := range users {
		userInfos[i] = *s.mapListUsersRowToUserInfo(&user)
	}

	totalPages := int(count) / req.PageSize
	if int(count)%req.PageSize > 0 {
		totalPages++
	}

	return &ListUsersResponse{
		Users:      userInfos,
		TotalCount: count,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}, nil
}

// UpdateUserStatus updates user status (admin only)
func (s *Service) UpdateUserStatus(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error) {
	user, err := s.queries.UpdateUserStatus(ctx, sqlc.UpdateUserStatusParams{
		ID:     userID,
		Status: status,
	})
	if err != nil {
		s.logger.Error("failed to update user status", "error", err, "user_id", userID)
		return nil, ErrInternalError
	}

	return s.mapUpdateUserStatusRowToUserInfo(&user), nil
}

// Helper methods to convert database rows to UserInfo

func (s *Service) mapGetUserByIDRowToUserInfo(user *sqlc.GetUserByIDRow) *Info {
	userInfo := &Info{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.UserRole,
		Status:   user.Status,
	}

	if user.CompanyID.Valid {
		userInfo.CompanyID = &user.CompanyID.Int32
	}
	if user.CompanyName.Valid {
		userInfo.CompanyName = &user.CompanyName.String
	}
	if user.JobTitle.Valid {
		userInfo.JobTitle = &user.JobTitle.String
	}
	if user.Mobile.Valid {
		userInfo.Mobile = &user.Mobile.String
	}

	return userInfo
}

func (s *Service) mapUpdateUserRowToUserInfo(user *sqlc.UpdateUserRow) *Info {
	userInfo := &Info{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.UserRole,
		Status:   user.Status,
	}

	if user.CompanyID.Valid {
		userInfo.CompanyID = &user.CompanyID.Int32
	}
	if user.JobTitle.Valid {
		userInfo.JobTitle = &user.JobTitle.String
	}
	if user.Mobile.Valid {
		userInfo.Mobile = &user.Mobile.String
	}

	return userInfo
}

func (s *Service) mapListUsersRowToUserInfo(user *sqlc.ListUsersRow) *Info {
	userInfo := &Info{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.UserRole,
		Status:   user.Status,
	}

	if user.CompanyID.Valid {
		userInfo.CompanyID = &user.CompanyID.Int32
	}
	if user.CompanyName.Valid {
		userInfo.CompanyName = &user.CompanyName.String
	}
	if user.JobTitle.Valid {
		userInfo.JobTitle = &user.JobTitle.String
	}
	if user.Mobile.Valid {
		userInfo.Mobile = &user.Mobile.String
	}

	return userInfo
}

func (s *Service) mapUpdateUserStatusRowToUserInfo(user *sqlc.UpdateUserStatusRow) *Info {
	userInfo := &Info{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.UserRole,
		Status:   user.Status,
	}

	if user.CompanyID.Valid {
		userInfo.CompanyID = &user.CompanyID.Int32
	}
	if user.JobTitle.Valid {
		userInfo.JobTitle = &user.JobTitle.String
	}
	if user.Mobile.Valid {
		userInfo.Mobile = &user.Mobile.String
	}

	return userInfo
}

// Service errors
var (
	ErrUserNotFound    = errors.New("user not found")
	ErrInvalidPassword = errors.New("invalid password")
	ErrWeakPassword    = errors.New("password does not meet strength requirements")
	ErrInternalError   = errors.New("internal server error")
)
